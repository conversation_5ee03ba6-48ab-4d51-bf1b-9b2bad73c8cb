<template>
  <el-tabs v-model="activeName" class="planMain">
    <el-tab-pane label="防护用品清单" name="list">
      <ProductManagement v-if="activeName === 'list'" />
    </el-tab-pane>

    <!-- <el-tab-pane label="配发标准" name="plan">
      <div v-if="activeName === 'plan'" style="text-align: left">
        <protection-plan :productList="listData" ref="protectionPlan" :planHandleUpload="planHandleUpload"></protection-plan>
      </div>
    </el-tab-pane> -->
    <el-tab-pane label="新配发标准" name="newPlan">
      <div v-if="activeName === 'newPlan'" style="text-align: left">
        <new-protection-plan />
      </div>
    </el-tab-pane>
    <el-tab-pane label="领用记录" name="record">
      <DefendManage v-if="activeName === 'record'" :date="date" />
    </el-tab-pane>
    <el-tab-pane label="领用记录(岗位)" name="recordByStation">
      <DefendManageByStation v-if="activeName === 'recordByStation'" :date="date" />
    </el-tab-pane>
    <el-tab-pane label="领用申请" name="applicationList">
      <applicationList />
    </el-tab-pane>
    <el-tab-pane label="报废列表" name="scrapList">
        <scrapList />
    </el-tab-pane>
    <el-tab-pane v-if="branch==='by'" label="安全帽台帐" name="hardhat">
        <hardhat />
    </el-tab-pane>

    <el-tab-pane label="分类管理" name="categoryManagement">
        <protection-category-management ref="categoryManagement"  v-if="activeName === 'categoryManagement'"/>
    </el-tab-pane>
  </el-tabs>
</template>
<script>
import { findMillConstruction, saveProgrammePlan, saveDefendProductList,getDefendProductList,saveProtectionPlan, saveOneprotection, delOneprotection,savePageInfo,delCategory,immediatePPEPlan, findHarmFactors,getBranch, getProductList} from "@/api/index";
import { getProtectionCategoryTree } from '@/api/protectionCategory';
import _ from "lodash";
import protectionPlan from "@/components/protectionPlan.vue";
import NewProtectionPlan from "@/components/NewProtectionPlan.vue";
import WarehouseSelector from "@/components/WarehouseSelector.vue";
import DefendManage from './DefendManage'
import DefendManageByStation from './DefendManageByStation'
import scrapList from "@/components/scrapList.vue";
import hardhat from "@/components/hardhat.vue";
import applicationList from "@/components/applicationList.vue";
import ProtectionCategoryManagement from "@/components/ProtectionCategoryManagement.vue";
import ProductManagement from './ProductManagement.vue';

export default {
  name: 'programmePlan',
  props: {
    programmePlanView: Boolean,
  },
  data(){
      return {
        branch:'',
        listData: [], //防护用品清单数据
        activeName: 'list', // 默认打开防护用品清单
        listActiveName:'',
        selectedWarehouse: '', // 当前选中的仓库ID
        currentWarehouse: null, // 当前选中的仓库信息
        loadingData: false, // 防止重复加载数据
        protectiveCyc: "",
        protectiveEquipment: "",
        protective:[], // 个人防护多选框的值
        protectiveEquipmentOptions:[],
        harmFactors: [],
        loading: false,
        form:{
          harmFactors: "",
        },
        props: {
          multiple: true,
          children: "harmFactors",
          value: "label",
        },
        addPage:{
          name:"",
          column:"",
          id:"",
        },
        // 分类相关数据
        categoryTree: [], // 分类树数据
        categoryProps: {
          label: 'name',
          value: '_id',
          children: 'children',
          checkStrictly: true, // 允许选择任意节点
          emitPath: true // 返回完整的路径数组
        },
        // 新的防护用品清单数据
        allProductList: [], // 所有防护用品数据（扁平化）
        filteredProductList: [], // 过滤后的防护用品数据
        flatCategoryList: [], // 扁平化的分类列表（已废弃，改用级联选择器）
        selectedCategory: '', // 选中的分类ID（已废弃）
        searchKeyword: '', // 搜索关键词
        // 新的筛选条件
        selectedCategoryPath: [], // 级联选择器选中的分类路径
        categoryFilterProps: {
          label: 'name',
          value: '_id',
          children: 'children',
          checkStrictly: true, // 允许选择任意节点
          emitPath: false // 只返回选中节点的值
        },
        dialogForm:false,
        fromDrawer:false,
        // activeNode: {},
        expandedIds: [],
        excelData: {
          header: null,
          results: null
        },
        formFields: [],
        pictureList: [],
        staticUrl: '',
        // planList: [], // 岗位的防护用品计划
        keyword: '',
        importType: 1, // 1 覆盖 2 追加
        // 智能导入相关
        smartImportDialogVisible: false,
      }
  },
  computed: {
    options(){
      return [{
          label:this.branch==='wh'?'商品SKU':'物料编码',
          value:0,
          key: 'materialCode',
          required: true,
          }, {
          label:this.branch==='wh'?'商品名称':'产品名称',
          value:0,
          key: 'product',
          required: true,
          }, {
          label:this.branch==='wh'?'商品规格':'产品规格',
          value:0,
          key: 'productSpec',
          required: true,
          }, {
            label:"型号",
            value:1,
            key: 'modelNumber',
            required: false,
          },{
            label:"使用方式",
            value:2,
            key: 'useMethod',
          }, {
            label:"样式",
            value:3,
            key: 'style',
          },{
            label:"可配滤盒数",
            value:4,
            key: 'accessoryInfo',
          }, {
            label:"分类",
            value:5,
            key: 'classification',
          },{
            label:"防护类别",
            value:6,
            key: 'protectionType',
          },{
            label:"APF",
            value:7,
            key: 'APF',
          },{
            label:"防护用途",
            value:8,
            key: 'function',
          },{
            label:"适用危害因素",
            value:9,
            key: 'harmFactors',
          },{
            label:"面屏厚度",
            value:10,
            key: 'screenThickness',
          },{
            label:"特点",
            value:11,
            key: 'characteristic',
          },{
            label:"厂家",
            value:12,
            key: 'vender',
          },{
            label:"使用行业/环境",
            value:13,
            key: 'industryEnvironment',
          },{
            label:"NRR（dB）",
            value:14,
            key: 'NRR',
          },{
            label:"SNR（dB）",
            value:15,
            key: 'SNR',
          },{
            label:"包装",
            value:16,
            key: 'packing',
          },{
            label:"库存",
            value:17,
            key: 'surplus',
            required: true,
          },
          // 图片字段已隐藏
          // {
          //   label:"图片",
          //   value:18,
          //   key: 'picture',
          // },
          {
            label:"防护用品分类",
            value:19,
            key: 'categorySelector',
            required: false,
          },
      ]
    }
  },
  watch: {
    activeName(newVal, oldVal) {
      console.log('标签页切换:', oldVal, '->', newVal);
      // 当切换到分类管理标签页时，确保数据已加载
      if (newVal === 'categoryManagement') {
        console.log('切换到分类管理标签页');
        // 延迟执行，确保组件已经渲染
        this.$nextTick(() => {
          const categoryComponent = this.$refs.categoryManagement;
          if (categoryComponent && categoryComponent.loadCategoryTree) {
            categoryComponent.loadCategoryTree();
          }
        });
      }
    },
    dialogForm(val) {
      if (!val) {
        // 清空
       this.addPage = {
          name:"",
          column:"",
          id:'',
        }
      }
    },
    "form._id": {
      async handler() {
        if (this.form.harmFactors && this.form.harmFactors.length > 0) {
          // this.form.harmFactors = this.form.harmFactors.map((item) => item[1]);
          await this.getHarmFactor({
            query: { names: this.form.harmFactors },
          });
        }
      },
      immediate: true, //立即执行
    },
    // 监听分类选择变化（级联选择器）
    selectedCategoryPath() {
      // 分类变化时重新获取数据（后端筛选）
      this.getDefendProductList();
    },
    // 监听搜索关键词变化 - 移除自动搜索，改为手动点击查询按钮
    // searchKeyword() {
    //   this.getDefendProductList();
    // },
  },
  methods:{
    // 处理仓库切换
    handleWarehouseChange(warehouse) {
      this.currentWarehouse = warehouse;
      console.log('切换到仓库:', warehouse);
      console.log('当前selectedWarehouse:', this.selectedWarehouse);

      // 确保selectedWarehouse已更新
      if (warehouse && warehouse._id) {
        this.selectedWarehouse = warehouse._id;
      }

      console.log('更新后的selectedWarehouse:', this.selectedWarehouse);

      // 重新加载防护用品清单数据
      this.getDefendProductList();
    },

    handleInputChange(key) {
      if (key === 'surplus') {
        const str = this.form[key]
        if (!/^\d*\.?\d+$/.test(str)) {
          this.form[key] = ''
          this.$message({
            type: 'warning',
            message: '请输入正确的库存'
          })
        }
      }
    },
    onSelectHarmFactors(e) {
      console.log(e, 'dawdaw');
    },
    async getHarmFactor(query) {
      let res = await findHarmFactors(query);
      if (res.status === 200) {
        this.harmFactors = res.data.data;
      }
    },
    async remoteMethod(query) {
      if (query !== "") {
        this.loading = true;
        await this.getHarmFactor({ query: JSON.stringify({ name: query }) });
        this.loading = false;
      } else {
        this.harmFactors = [];
      }
    },
    filterData(data) {
      if (!this.keyword) {
        return data
      }
      return data.filter(e => {
        return (e.product.includes(this.keyword) || e.materialCode.includes(this.keyword))
      })
    },
    formatSrc(item) {
      if (item.picture) {
        return item.picture
      }
    },
    delCategory(){
      this.$confirm('确认删除该分类?将会删除分类下的所有内容', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async () => {
          await delCategory({_id:this.addPage.id}).then(res => {
            console.log(this.addPage.id,res,'删除该类');
                if(res.status == 200){
                    this.$message({
                        message: '删除成功',
                        type: 'success'
                    });
                    this.dialogForm = false;
                    this.getDefendProductList();
                }
            })
        }).catch((err) => {
          console.log(err, '删除防护用品分类错误');
            this.$message({
                type: 'info',
                message: '已取消操作'
            });
        });
      
    },
    plus(){
      this.planList.push({
        name: [],
        num : '-'
      })
    },
    delConfrim(row, categoryId) {
      this.$confirm('是否删除该数据?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async () => {
          await this.del(categoryId, row._id)
        }).catch(() => {});
    },
    async del(categoryId, dataId) {
      const res = await delOneprotection({ categoryId, dataId })
      if (res && res.code === 200) {
        this.$message({
          type: 'success',
          message: '删除成功'
        })
      }
      this.getDefendProductList(false);
      this.form = {}
    },
    edit(row, name, item){
      this.fromDrawer = true;

      // 如果item为null（统一表格模式），显示所有常用字段包括分类选择器
      if (!item || !item.tableHeader) {
        this.formFields = this.options.filter(e => [
          'materialCode', 'product', 'productSpec', 'modelNumber',
          'protectionType', 'harmFactors', 'vender', 'surplus',
          'picture', 'categorySelector'
        ].includes(e.key));
      } else {
        // 原有的基于tableHeader的过滤逻辑
        this.formFields = this.options.filter(e => item.tableHeader.includes(e.key));
      }

      this.form = {}

      // 修复：如果是新增（name为空），使用默认分类名称或从分类选择中获取
      if (!name || name === '') {
        // 新增时，先不设置name，等用户选择分类后再设置
        this.form.name = null;
      } else {
        this.form.name = name;
      }
      if (row) {
        const data = JSON.parse(JSON.stringify(row))
        Object.keys(data).forEach(key => {
          // this.form[key] = data[key]
          this.$set(this.form, key, data[key])
        })
        if (row.picture) {
          this.$set(this.form, 'pictureSrc', this.formatSrc(row))
        }

        // 设置分类选择器的值
        if (row.categoryId) {
          this.$set(this.form, 'categoryPath', row.categoryId)
        }
      }
      console.log(999999, this.form)
    },
    
    closePanel() {
      this.fromDrawer = false
    },
    async onSubmit(){ //提交、
      const requiredFields = this.options.filter(e => e.required)

      let isVaild = true
      for (let i = 0; i < requiredFields.length; i++) {
        const requiredField = requiredFields[i]
        if (!this.form[requiredField.key]) {
          isVaild = false
          break;
        }
      }

      if (!isVaild) {
        this.$message({
          type: 'warning',
          message: '请完善内容'
        })
        return
      }
      // 如果是新增的话要校验物料编码是否重复
      if (!this.form._id) {
        const isHasSameCode = this.listData.some(ds => ds.data.some(item => item.materialCode === this.form.materialCode))
        if (isHasSameCode) {
          this.$message({
            type: 'warning',
            message: '已存在该物料编码'
          })
          return
        }
      }
      
      // 添加仓库ID到表单数据
      const formData = {
        ...this.form,
        warehouseId: this.selectedWarehouse
      };

      const res = await saveOneprotection({data: formData})
      this.$message({
        type: 'success',
        message: '单个防护用品信息保存成功！'
      })
      this.fromDrawer = false;
      this.getDefendProductList(false);
      this.form = {}
    },
    openDialog(name, column, id){ // 编辑已有标签页
        this.dialogForm = true;
        this.addPage.name = name;
        this.addPage.column = column;
        this.addPage.id = id;
    },
    async savePageInfo(){
      console.log('保存新分类或旧分类');
      const data = {};
      data.name = this.addPage.name;
      data.column = JSON.stringify(this.addPage.column);
      data.id = this.addPage.id;
      console.log(data, this.listData)

      if (!this.addPage.id) {
        // 判断是否重复
        const isExists = this.listData.some(e => e.name === this.addPage.name)
        if (isExists) {
          this.$message({
            type: 'warning',
            message: "该分类已存在，请勿重复创建",
          })
          return
        }
      }

      await savePageInfo(data).then( //  保存防护用品清单
        this.$message({
          type: 'success',
          message: '分类信息保存成功!'
        })
      );
      this.dialogForm = false
      this.getDefendProductList();
      this.addPage = {
          name:"",
          column:"",
          id:'',
        };
    },
    handleUpload() { // 导入用品清单
      this.$refs['excel-upload-input'].click()
    },
    planHandleUpload() {
      this.$refs['excel-upload-inputPlan'].click()
    },
    planHandleClick(e){
      const files = e.target.files
      const rawFile = files[0] // only use files[0]
      if (!rawFile) return
      this.$refs['excel-upload-inputPlan'].value = null // fix can't select the same excel
      if (!this.beforeUpload) {
        this.readerPlanData(rawFile)
        return
      }
      const before = this.beforeUpload(rawFile)
      if (before) {
        this.readerPlanData(rawFile)
      }
    },
    handleClick(e) {
      const files = e.target.files
      const rawFile = files[0] // only use files[0]
      if (!rawFile) return
      this.upload(rawFile)
    },
    upload(rawFile) {
      this.$refs['excel-upload-input'].value = null // fix can't select the same excel
      if (!this.beforeUpload) {
        this.readerData(rawFile)
        return
      }
      const before = this.beforeUpload(rawFile)
      if (before) {
        this.readerData(rawFile)
      }
    },
    readerData(rawFile) {
      this.loading = true
      return new Promise((resolve) => {
        const reader = new FileReader()
        reader.onload = async e => {
          const data = e.target.result
          let XLSX = require('xlsx');
          const workbook = XLSX.read(data, { type: 'array',cellDates: true })
          const protectiveData = [];

          const results = XLSX.utils.sheet_to_json(workbook.Sheets[workbook.SheetNames[0]], {defval: ''});
          results.forEach(result => {
            const item = {
              name: '防护用品'
            };
            for(let key in result){
              switch(key){
                case '分类':
                  item.name = result[key];
                  break;
                case '物料编码':
                  item.materialCode = result[key];
                  break;
                case '规格':
                  item.productSpec = result[key];
                  break;
                case '产品':
                  item.product = result[key];
                  break;
                case '样式':
                case '面罩样式':
                  item.style = result[key];
                  break;
                case '可配滤盒数':
                  item.accessoryInfo = result[key];
                  break;
                case '型号':
                  item.modelNumber = result[key];
                  break;
                case '防护类别':
                  item.protectionType = result[key];
                  break;
                case 'APF':
                  item.APF = result[key];
                  break;
                case '包装':
                  item.packing = result[key];
                  break;
                case '库存':
                  item.surplus = result[key];
                  break;
                case '使用方式':
                  item.useMethod = result[key];
                  break;
                case '厂家':
                  item.vender = result[key];
                  break;
                case '特点':
                  item.characteristic = result[key];
                  break;
                case '面屏厚度':
                  item.screenThickness = result[key];
                  break;
                case '适用行业/环境':
                  item.industryEnvironment = result[key];
                  break;
                case '适用危害因素':
                  item.harmFactors = result[key];
                  break;
                case 'NRR（dB）':
                  item.NRR = result[key];
                  break;
                case 'SNR（dB）':
                  item.SNR = result[key];
                  break;
                case '防护用途':
                  item.function = result[key];
                  break;
              }
            }
            protectiveData.push(item);
          })

          // 校验数据完整性
          const isErrData = []
          protectiveData.forEach((item, index) => {
            if (!item.name) {
              isErrData.push(`第${index + 1}条数据缺少：产品分类`)
            }
            if (!item.materialCode) {
              isErrData.push(`第${index + 1}条数据缺少：物料编码`)
            }
            if (!item.product) {
              isErrData.push(`第${index + 1}条数据缺少：产品名称`)
            }
            // if (!item.productSpec) {
            //   isErrData.push(`第${index + 1}条数据缺少：产品规格`)
            // }
          })
          if (isErrData.length > 0) {
            this.$notify({
              title: '警告',
              dangerouslyUseHTMLString: true,
              message: isErrData.join('，'),
              type: 'warning'
            });
            return
          }
          const result  = _.groupBy(protectiveData, 'name');
          // console.log(protectiveData, '-----------------');
          await saveDefendProductList({
            data: result,
            importType: this.importType,
            warehouseId: this.selectedWarehouse, // 添加仓库ID
          }).then( //  保存防护用品清单
            this.$message({
              type: 'success',
              message: '防护用品清单保存成功 !'
            })
          ) // 保存防护用品清单
          this.getDefendProductList();
          // this.generateData({ header, results });
          // this.loading = false
          resolve()
        }
        reader.readAsArrayBuffer(rawFile)
      })
    },
    readerPlanData(rawFile){ // 解析计划excel
      this.loading = true
      return new Promise((resolve) => {
        const reader = new FileReader()
        reader.onload = async e => {
          const data = e.target.result
          let XLSX = require('xlsx');
          const workbook = XLSX.read(data, { type: 'array',cellDates: true })
          const protectiveData = [];
          const sheetNameLength = workbook.SheetNames.length;
          const results = XLSX.utils.sheet_to_json(workbook.Sheets[workbook.SheetNames[0]], {defval: ''})
          const plan = [];
          results.forEach(result => { // 口罩
              const item = {};
              for(let key in result){
                switch(key){
                  case '厂房':
                    item.workshop = result[key];
                    break;
                  case '车间':
                    item.workspaces = result[key];
                    break;
                  case '岗位':
                    item.workstation = result[key];
                    break;
                  case '防护用品':
                    item.product = result[key];
                    break;
                  case '型号':
                    item.modelNumber = result[key];
                    break;
                  case '数量':
                    item.number = result[key];
                    break;
                  case '周期':
                    item.time = result[key];
                    break;
                }
              }
              plan.push(item);
          })
          console.log(plan, '传到后端的数据');
          await saveProtectionPlan(plan).then( //  保存防护用品清单
            this.$message({
              type: 'success',
              message: '发放计划保存成功 !'
            })
          ) // 保存防护用品清单
          this.$refs.protectionPlan.getPlan();
          immediatePPEPlan()
          // this.loading = false
          resolve()
        }
        reader.readAsArrayBuffer(rawFile)
      })
    },
    getHeaderRow(sheet) {
      const headers = []
      const range = XLSX.utils.decode_range(sheet['!ref'])
      let C
      const R = range.s.r
      /* start in the first row */
      for (C = range.s.c; C <= range.e.c; ++C) { /* walk every column in the range */
        const cell = sheet[XLSX.utils.encode_cell({ c: C, r: R })]
        /* find the cell in the first row */
        let hdr = 'UNKNOWN ' + C // <-- replace with your desired default
        if (cell && cell.t) hdr = XLSX.utils.format_cell(cell)
        headers.push(hdr)
      }
      return headers
    },

    getDefendProductList(handleActiveName = true){
      // 防止重复调用
      if (this.loadingData) {
        console.log('获取防护用品清单 - 正在加载中，跳过重复请求');
        return;
      }

      this.loadingData = true;

      // 构建请求数据，包含仓库ID和筛选参数
      const data = {};
      if (this.selectedWarehouse) {
        data.warehouseId = this.selectedWarehouse;
      }

      // 添加搜索关键词
      if (this.searchKeyword && this.searchKeyword.trim()) {
        data.searchKeyword = this.searchKeyword.trim();
      }

      // 添加分类筛选参数
      if (this.selectedCategoryPath && this.selectedCategoryPath.length > 0) {
        // 如果是级联选择器，传递完整的路径数组
        data.categoryPath = this.selectedCategoryPath;
      }

      console.log('获取防护用品清单 - 当前仓库ID:', this.selectedWarehouse);
      console.log('获取防护用品清单 - 搜索关键词:', this.searchKeyword);
      console.log('获取防护用品清单 - 分类筛选:', this.selectedCategoryPath);
      console.log('获取防护用品清单 - 请求数据:', data);

      getDefendProductList(data)
        .then(res => {
          if (res.status === 200) {
            console.log('防护用品清单响应:', res.data);
            
            // 处理返回的数据
            if (res.data && res.data.list) {
              this.defendProductList = res.data.list;
              
              // 扁平化数据用于表格显示
              this.allProductList = this.flattenProductData(res.data.list);
              this.filteredProductList = [...this.allProductList];
              
              console.log('处理后的防护用品列表:', this.defendProductList);
              console.log('扁平化的产品列表:', this.allProductList);
            } else {
              this.defendProductList = [];
              this.allProductList = [];
              this.filteredProductList = [];
            }
          } else {
            this.$message.error(res.message || '获取防护用品清单失败');
          }
        })
        .catch(error => {
          console.error('获取防护用品清单失败:', error);
          this.$message.error('获取防护用品清单失败');
        })
        .finally(() => {
          this.loadingData = false;
        });
    },
    download() { // 下载清单模板
      let a = document.createElement("a");
      a.href = `/static/dataTemplate/防护用品清单管理.xlsx`;
      a.click();
    },
    planDownload(){ // 下载计划清单模板
      let a = document.createElement("a");
      a.href = `/static/dataTemplate/防护用品发放计划.xlsx`;
      a.click();
    },

    // 智能导入相关方法
    handleSmartImport() {
      this.smartImportDialogVisible = true;
    },

    // 智能导入成功后的处理
    handleSmartImportSuccess() {
      this.smartImportDialogVisible = false;
      // 刷新当前标签页的数据
      if (this.activeName === 'productManagement') {
        // 如果在产品管理页面，让子组件自己处理
        return;
      } else {
        // 如果在防护用品清单页面，需要同时查询新旧数据
        this.refreshProductData();
      }
    },

    // 刷新产品数据（同时查询新旧数据源）
    async refreshProductData() {
      this.loadingData = true;
      try {
        // 查询新的产品数据
        const newProductResponse = await getProductList({
          page: 1,
          limit: 1000, // 获取所有数据
          warehouseId: this.selectedWarehouse,
          isActive: true
        });

        // 查询传统的清单数据
        const data = {
          warehouseId: this.selectedWarehouse,
          searchKeyword: this.searchKeyword,
          categoryId: this.selectedCategoryPath.length > 0 ? this.selectedCategoryPath[this.selectedCategoryPath.length - 1] : ''
        };

        const oldProductResponse = await getDefendProductList(data);

        // 合并数据
        let allProducts = [];

        // 处理新产品数据
        if (newProductResponse.status === 200 && newProductResponse.data.list) {
          allProducts = [...newProductResponse.data.list];
        }

        // 处理传统清单数据
        if (oldProductResponse.status === 200 && oldProductResponse.data && oldProductResponse.data.list) {
          const flattenedOldData = this.flattenProductData(oldProductResponse.data.list);
          allProducts = [...allProducts, ...flattenedOldData];
        }

        // 去重（基于产品名称和型号）
        const uniqueProducts = [];
        const seen = new Set();

        allProducts.forEach(product => {
          const key = `${product.product}_${product.modelNumber || ''}_${product.materialCode || ''}`;
          if (!seen.has(key)) {
            seen.add(key);
            uniqueProducts.push(product);
          }
        });

        this.allProductList = uniqueProducts;
        this.filteredProductList = [...uniqueProducts];

        console.log('合并后的产品列表:', uniqueProducts);

      } catch (error) {
        console.error('刷新产品数据失败:', error);
        this.$message.error('刷新数据失败: ' + error.message);
      } finally {
        this.loadingData = false;
      }
    },
    handleTabsEdit(targetName, action) {
      if (action === 'add') { //增加标签页
        console.log('增加标签页-----------------');
        this.dialogForm = true;
      }
    },
    handleAvatarSuccess(res, file) {
      if (res.status === 200) {
        res.data.staticSrc = res.data.staticSrc.replace(/\\/g, "/");
        this.$set(this.form, 'picture', res.data.filename)
        this.$set(this.form, 'pictureSrc', res.data.staticSrc)
        this.$forceUpdate()
      }
    },
    // 加载分类树数据
    async loadCategoryTree() {
      try {
        const res = await getProtectionCategoryTree({
          includeSystem: true,
          activeOnly: true
        });
        if (res.status === 200) {
          this.categoryTree = res.data || [];
          console.log('加载的分类树数据:', this.categoryTree);
          // 生成扁平化的分类列表用于下拉选择
          this.flatCategoryList = this.flattenCategoryTree(this.categoryTree);
          console.log('扁平化的分类列表:', this.flatCategoryList);
        }
      } catch (error) {
        console.error('加载分类数据失败:', error);
      }
    },
    // 处理分类选择变化
    handleCategoryChange(selectedValue) {
      if (selectedValue) {
        // 查找选中的分类信息
        const selectedCategory = this.findCategoryById(selectedValue, this.categoryTree);
        if (selectedCategory) {
          // 检查是否为叶子节点
          if (selectedCategory.children && selectedCategory.children.length > 0) {
            this.$message.warning('请选择具体的防护用品分类（叶子节点）');
            this.form.categoryPath = null;
            return;
          }

          // 保存分类信息
          this.form.categoryId = selectedCategory._id;
          this.form.categoryName = selectedCategory.name;
          this.form.categoryPath = selectedCategory.path;

          // 修复：如果是新增模式且name为空，使用分类名称作为name
          if (!this.form.name) {
            this.form.name = selectedCategory.name;
          }
        }
      } else {
        // 清空分类信息
        this.form.categoryId = null;
        this.form.categoryName = null;
        this.form.categoryPath = null;

        // 如果清空分类且是新增模式，也清空name
        if (!this.form._id) {
          this.form.name = null;
        }
      }
    },

    // 根据ID查找分类
    findCategoryById(id, categories) {
      for (const category of categories) {
        if (category._id === id) {
          return category;
        }
        if (category.children && category.children.length > 0) {
          const found = this.findCategoryById(id, category.children);
          if (found) {
            return found;
          }
        }
      }
      return null;
    },

    // 扁平化分类树，用于下拉选择
    flattenCategoryTree(categories, result = []) {
      categories.forEach(category => {
        result.push({
          _id: category._id,
          name: category.name,
          path: category.path || category.name
        });
        if (category.children && category.children.length > 0) {
          this.flattenCategoryTree(category.children, result);
        }
      });
      return result;
    },

    // 扁平化防护用品数据
    flattenProductData(listData) {
      const result = [];
      listData.forEach(category => {
        if (category.data && category.data.length > 0) {
          category.data.forEach(product => {
            result.push({
              ...product,
              // 如果没有新的分类信息，使用旧的分类名称
              categoryPath: product.categoryPath || category.name,
              categoryName: product.categoryName || category.name,
              categoryId: product.categoryId || null
            });
          });
        }
      });
      return result;
    },

    // 搜索和筛选
    handleSearch() {
      // 调用后端API重新获取筛选后的数据
      this.getDefendProductList();
    },

    // 处理级联选择器的分类筛选变化
    handleCategoryFilterChange(selectedValue) {
      // 因为设置了emitPath: true，selectedValue是完整的路径数组
      this.selectedCategoryPath = selectedValue || [];
      // 分类变化时自动重新获取数据
      this.getDefendProductList();
    },
    // 新增防护用品
    handleAddProduct() {
      this.edit(null, '', null);
    },

    // 编辑防护用品
    editProduct(product) {
      this.edit(product, '', null);
    },

    // 删除防护用品
    deleteProduct(product) {
      this.delConfrim(product, null);
    },

    // 将分类数据转换为扁平化的产品列表
    updateProductList() {
      const allProducts = [];

      // 遍历所有分类
      if (this.listData && this.listData.length > 0) {
        this.listData.forEach(category => {
          if (category.data && category.data.length > 0) {
            category.data.forEach(product => {
              // 为每个产品添加分类信息
              const productWithCategory = {
                ...product,
                categoryName: product.categoryName || category.name,
                // 保持原有的 categoryPath，如果没有则使用分类名称
                categoryPath: product.categoryPath || category.name,
                // 确保有必要的字段
                materialCode: product.materialCode || '',
                product: product.product || '',
                productSpec: product.productSpec || '',
                modelNumber: product.modelNumber || '',
                protectionType: product.protectionType || '',
                harmFactors: product.harmFactors || [],
                vender: product.vender || '',
                surplus: product.surplus || 0,
                picture: product.picture || ''
              };
              allProducts.push(productWithCategory);
            });
          }
        });
      }

      console.log('转换后的产品列表:', allProducts);

      // 更新产品列表
      this.allProductList = allProducts;
      this.filteredProductList = [...allProducts];
    },

    // 分类筛选变化处理
    handleCategoryFilterChange() {
      // 分类变化时调用后端API重新获取数据
      this.getDefendProductList();
    },

    // 搜索处理
    handleSearch() {
      // 搜索时调用后端API重新获取数据
      this.getDefendProductList();
    },

    // 格式化分类路径显示
    formatCategoryPath(categoryPath) {
      if (!categoryPath) return '';
      // 如果以 / 开头，去掉开头的 /
      return categoryPath.startsWith('/') ? categoryPath.substring(1) : categoryPath;
    }

  },
  components: {
    protectionPlan,
    NewProtectionPlan,
    DefendManage,
    DefendManageByStation,
    scrapList,
    applicationList,
    hardhat,
    WarehouseSelector,
    ProtectionCategoryManagement,
    ProductManagement
  },
  async created() {
    // 不在这里调用getDefendProductList，等待仓库选择器初始化完成后再调用
    const { type, date } = this.$route.query
    if (type) {
      this.activeName = type
    }
    if (date) {
      this.date = date
    }

    const res = await getBranch()
    if (res.status === 200) {
      // 存储到vuex里
      const branch = res.data
      this.branch = branch
      this.$store.commit('protectionCloudManagement/setBranch', branch)
    }

    // 加载防护用品分类数据
    await this.loadCategoryTree()
  }
}
</script>
<style scoped lang="scss">
.planMain{
  padding:1.3%;
}

.warehouse-selector-container {
  margin-bottom: 20px;
}

.custom-tree-node {
  display: flex;
  align-items: center;
  font-size: 12.5px;
  padding-right: 8px;
}

.treeRight{
  width: 40%;
  position: absolute;
  right: 15%;
}
.plus{
  position: absolute;
  right: 10%;
}
.excel-upload-input{
  display: none;
  z-index: -9999;
}

::v-deep  .listMain .el-tabs__item {
    line-height: 40px;
    border: 2px solid #E0E4EB;
    height: 40px;
    padding:0 15px !important;
    font-size: 14px;
}

::v-deep  .listMain .el-tabs__header.is-left {
  margin:0;
  padding-top:8px;
  text-align:center;
}
::v-deep  .listMain .el-tabs__nav-next {
  display: none
}
::v-deep  .listMain .el-tabs__nav-prev {
  display: none
}
::v-deep  .listMain .el-tabs__new-tab{
    float: none;
    border:0;
    font-size:27px;
    color:#0063E0;
    margin:0;
}
.listMain {
  background-color: #F5F7FA;
}
::v-deep  .listMain .el-icon-close {
  display: none
}
.dialog {
  width:60%;
  margin-left:22%;
  margin-top:7%;
  z-index:999999;
}

.button {
  background: #0063E0;
  position: absolute;
  right: 0;
}
::v-deep .el-checkbox__input {
    padding-right: 7px;
}

.topBar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

::v-deep  .avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
  .avatar-uploader .el-upload:hover {
    border-color: #409EFF;
  }
  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 100px;
    height: 100px;
    line-height: 100px;
    text-align: center;
  }
  .avatar {
    width: 100px;
    height: 100px;
    display: block;
  }
  
  ::v-deep  .el-drawer__body {
    overflow-y: scroll;
  }
</style>