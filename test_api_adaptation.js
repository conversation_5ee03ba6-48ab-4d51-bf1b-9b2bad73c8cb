/**
 * 防护用品API适配测试脚本
 * 用于验证新的API接口是否正确返回数据结构
 */

// 模拟测试数据
const testData = {
  // 模拟员工信息
  employee: {
    _id: 'test_employee_id',
    name: '测试员工',
    category: '1', // 生产岗位
    departs: [ 'test_department_id' ],
    companyId: [ 'test_company_id' ],
  },

  // 模拟仓库信息
  warehouse: {
    _id: 'test_warehouse_id',
    name: '测试仓库',
    EnterpriseID: 'test_company_id',
    isPublic: true,
    managementScope: [{
      fullId: 'mill_id/workspace_id/station_id',
      level: 'stations',
    }],
  },

  // 模拟防护用品产品
  protectiveProducts: [
    {
      _id: 'product_1',
      EnterpriseID: 'test_company_id',
      warehouseId: 'test_warehouse_id',
      categoryId: 'category_1',
      categoryPath: '/头部防护/安全帽',
      categoryName: '安全帽',
      product: '3M安全帽',
      productSpec: 'H-700系列',
      modelNumber: 'H-701R',
      surplus: 100,
      isActive: true,
    },
  ],

  // 模拟配发标准
  protectionPlan: {
    _id: 'plan_1',
    EnterpriseID: 'test_company_id',
    nodeFullId: 'mill_id/workspace_id/station_id',
    nodeLevel: 'stations',
    nodeName: '测试岗位',
    products: [{
      _id: 'plan_product_1',
      categoryId: 'category_1',
      categoryPath: '/头部防护/安全帽',
      categoryName: '安全帽',
      product: '安全帽',
      number: 1,
      time: 6,
      timeUnit: 'M',
    }],
    planStatus: 1,
    configStatus: 'configured',
  },
};

// 验证API返回数据结构
function validateApiResponse(response) {
  console.log('🔍 验证API响应数据结构...');

  const { data } = response;
  const errors = [];

  // 验证基本结构
  if (!data) {
    errors.push('缺少data字段');
    return { valid: false, errors };
  }

  // 验证必需字段
  const requiredFields = [ 'records', 'typeList', 'applications', 'allProtectionPlan' ];
  requiredFields.forEach(field => {
    if (!data.hasOwnProperty(field)) {
      errors.push(`缺少必需字段: ${field}`);
    }
  });

  // 验证新增的仓库信息
  if (!data.employeeWarehouse) {
    errors.push('缺少新增的employeeWarehouse字段');
  } else {
    const warehouse = data.employeeWarehouse;
    if (!warehouse.warehouseId || !warehouse.warehouseName) {
      errors.push('employeeWarehouse字段结构不完整');
    }
  }

  // 验证typeList结构（新的产品模型）
  if (data.typeList && data.typeList.list) {
    data.typeList.list.forEach((category, index) => {
      if (!category._id || !category.name || !category.data) {
        errors.push(`typeList[${index}]结构不完整`);
      }

      if (category.data && Array.isArray(category.data)) {
        category.data.forEach((product, pIndex) => {
          const requiredProductFields = [ '_id', 'product', 'surplus' ];
          requiredProductFields.forEach(field => {
            if (!product.hasOwnProperty(field)) {
              errors.push(`typeList[${index}].data[${pIndex}]缺少字段: ${field}`);
            }
          });
        });
      }
    });
  }

  // 验证配发标准结构
  if (data.allProtectionPlan && Array.isArray(data.allProtectionPlan)) {
    data.allProtectionPlan.forEach((plan, index) => {
      if (!plan._id || !plan.products) {
        errors.push(`allProtectionPlan[${index}]结构不完整`);
      }

      if (plan.products && Array.isArray(plan.products)) {
        plan.products.forEach((product, pIndex) => {
          if (!product.product || typeof product.number !== 'number') {
            errors.push(`allProtectionPlan[${index}].products[${pIndex}]结构不完整`);
          }
        });
      }
    });
  }

  return {
    valid: errors.length === 0,
    errors,
    summary: {
      totalRecords: data.records ? data.records.length : 0,
      totalCategories: data.typeList && data.typeList.list ? data.typeList.list.length : 0,
      totalApplications: data.applications ? data.applications.length : 0,
      totalPlans: data.allProtectionPlan ? data.allProtectionPlan.length : 0,
      hasWarehouseInfo: !!data.employeeWarehouse,
    },
  };
}

// 模拟API响应数据
const mockApiResponse = {
  status: 200,
  data: {
    records: [],
    typeList: {
      list: [
        {
          _id: 'category_1',
          name: '头部防护',
          categoryPath: '/头部防护',
          tableHeader: 'static/images/protection/头部防护.png',
          data: [
            {
              _id: 'product_1',
              product: '3M安全帽',
              productSpec: 'H-700系列',
              modelNumber: 'H-701R',
              surplus: 100,
              materialCode: 'H123456',
              selectedNum: 0,
            },
          ],
        },
      ],
    },
    applications: [],
    allProtectionPlan: [
      {
        _id: 'plan_1',
        EnterpriseID: 'test_company_id',
        nodeFullId: 'mill_id/workspace_id/station_id',
        products: [
          {
            _id: 'plan_product_1',
            categoryId: 'category_1',
            product: '安全帽',
            number: 1,
            time: 6,
            timeUnit: 'M',
            todo: null,
          },
        ],
        employee: 'test_employee_id',
      },
    ],
    employeeWarehouse: {
      warehouseId: 'test_warehouse_id',
      warehouseName: '测试仓库',
      isPublic: true,
    },
  },
};

// 执行验证
console.log('🚀 开始验证防护用品API适配...\n');

const validation = validateApiResponse(mockApiResponse);

if (validation.valid) {
  console.log('✅ API响应数据结构验证通过！');
  console.log('📊 数据摘要:', validation.summary);
} else {
  console.log('❌ API响应数据结构验证失败！');
  console.log('🐛 发现的问题:');
  validation.errors.forEach((error, index) => {
    console.log(`   ${index + 1}. ${error}`);
  });
}

console.log('\n📋 验证完成');

// 导出验证函数供其他模块使用
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    validateApiResponse,
    testData,
    mockApiResponse,
  };
}
