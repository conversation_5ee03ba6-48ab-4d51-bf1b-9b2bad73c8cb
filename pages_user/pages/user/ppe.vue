<template>
	<gracePage headerBG="#008AFF" statusBarBG="#008AFF" :bounding="false">
		<my-header slot="gHeader" title="防护用品领用" />
		<view slot="gBody" class="tabs-container">
			<!-- 添加遮罩层，仅在标签切换过程中显示 -->
			<view v-if="isTabSwitching" class="tab-mask"></view>
			
			<u-tabs :list="tabList" :scrollable="true" @click="changeView" lineWidth="30" :activeStyle="{
					color: '#333333',
					fontWeight: 'bold',
				}" :inactiveStyle="{
					color: '#000000',
				}" itemStyle="padding: 0.5rem 0.5rem 0 1rem; height: 2rem;"></u-tabs>
			
			<!-- 领用 -->
			<view v-show="currentIndex === 0" class="tab-content">
				<view v-if="waitRecords.length > 0">
					<view class="receiveBox" v-for="item in waitApplyRecords" :key="item._id">
						<view class="contain">
							<view class="mainBox">
								<view class="recordTitle">{{ item.product }} {{ item.number }}件</view>
								<view class="type online" v-if="item.recordSource === 1">线上申请</view>
								<view class="type under" v-else>线下领用</view>
								<view class="content">
									<view v-for="product in item.products" :key="product._id">{{ product.product }}
										{{ product.modelNumber ? product.modelNumber : '' }} × {{ product.number }}
									</view>
								</view>
							</view>
							<view class="btnBar">
								<u-button type="primary" text="领用" @tap="receiveBtn(item, 'receive')"
									size="small"></u-button>
								<u-button type="warning" text="拒绝" @tap="receiveBtn(item, 'reject')"
									size="small"></u-button>
							</view>
						</view>
						<view class="timeBox">
							<view class="timeNotice" v-if="item.receiveStartDate" :class="warning ? warn : ''">
								请{{ item.warningDate ? '在' + item.warningDate + '前' : '及时' }}完成领取</view>
						</view>
					</view>
				</view>

				<view>
					<view class="receiveBox" v-for="item in allProtectionPlan" :key="item._id">
						<view class="receiveBoxTitle" v-if="item.grantType === 'mill'">
							工作场所：{{item.workspacesName ? item.workspacesName : ''}}/{{item.workstationName ? item.workstationName : ''}}
						</view>
						<view class="receiveBoxTitle" v-if="item.grantType === 'depart'">
							部门：{{item.departName}}
						</view>

						<view>
							<view style="border-bottom: 1px solid #c8c9cc;padding-bottom: 10px;margin-bottom: 10px;"
								class="contain" v-for="productItem in item.products" :key="productItem._id">
								<view class="mainBox">
									<view class="recordTitle">{{productItem.product}} * {{productItem.number}} </view>
									<view style="display: flex;">
										<view class="type under" style="padding-right: 0.5rem;">线下领用 </view>
										<view class="type under">
											{{productItem.time}}{{formatTimeUnit(productItem.timeUnit)}}/次
										</view>
									</view>
									<view class="content">
										领取时间：{{ formatTime(productItem.todo.receiveStartDate) }}
									</view>
								</view>
								<view class="btnBar" v-if="ableReceive(productItem.todo.receiveStartDate)">
									<u-button type="primary" text="领用"
										@tap="handleReceive(productItem, item, 'receive')" size="small"></u-button>
									<u-button type="warning" text="拒绝" @tap="handleReceive(productItem, item, 'reject')"
										size="small"></u-button>
								</view>
								<view style="width: 200px;" class="btnBar" v-else>
									<u-button type="info" text="未到领取时间" size="small"></u-button>
								</view>
							</view>
						</view>
					</view>

					<view v-if="allProtectionPlan.length === 0 && waitApplyRecords.length===0" class="imgBox">
						<img src="@/pages_user/static/box.svg" alt="">
						<view>暂无可领用的用品</view>
					</view>
				</view>
			</view>
			
			<!-- 申请 -->
			<view v-show="currentIndex === 1" class="tab-content center">
				<view v-if="appShow" class="list">
					<view v-if="userInfo.branch === 'wh'" class="center-body">
						<u-list height="84vh">
							<u-list-item v-for="item in navLeft" :key="item._id">
								<!-- 一级列表 -->
								<view class="list-box" @click="toggleExpand(item)">
									<view class="main-box">
										<view class="img-box">
											<img :src="item.tableHeader[0]" class="pngStyle" alt="" />
										</view>
										<view class="text-box">
											<view class="title">{{ item.name }}</view>
										</view>
										<view class="arrow-icon">
											<u-icon :name="item.isExpanded ? 'arrow-up' : 'arrow-down'" size="14"
												color="#666"></u-icon>
										</view>
									</view>
								</view>
								<!-- 二级列表 -->
								<view v-if="item.isExpanded" class="sub-list">
									<view v-for="subItem in item.data" :key="subItem._id" class="sub-item">
										<view class="text-box">
											<text>{{ subItem.productSpec }}</text>
										</view>
										<view class="select-box">
											<u-number-box v-model="subItem.selectedNum" min="0" max="100"
												:showMinus="subItem.selectedNum > 0"
												:showPlus="subItem.selectedNum < 100" @change="valChange(subItem,item)">
												<view slot="minus" class="minus">
													<u-icon name="minus" color="#FFFFFF" size="12"></u-icon>
												</view>
												<text slot="input" style="width: 50px;text-align: center;"
													class="input">{{ subItem.selectedNum }}</text>
												<view slot="plus" class="plus">
													<u-icon name="plus" color="#FFFFFF" size="12"></u-icon>
												</view>
											</u-number-box>
										</view>
									</view>
								</view>
							</u-list-item>
						</u-list>
					</view>
					<view v-else class="center-body">
						<u-list height="84vh">
							<u-list-item v-for="item in navLeft" :key="item._id">
								<view class="list-box">
									<view class="main-box">
										<view class="img-box">
											<img :src="item.tableHeader[0]" class="pngStyle" alt="" />
										</view>
										<view class="text-box">
											<view class="title">{{ item.name }}</view>
										</view>
									</view>
									<view class="select-box">
										<u-number-box v-model="item.selectedNum" min="0" max="100"
											:showMinus="item.selectedNum > 0" :showPlus="item.selectedNum < 100"
											@change="valChange(item)">
											<view slot="minus" class="minus">
												<u-icon name="minus" color="#FFFFFF" size="12"></u-icon>
											</view>
											<text slot="input" style="width: 50px;text-align: center;"
												class="input">{{ item.selectedNum }}</text>
											<view slot="plus" class="plus">
												<u-icon name="plus" color="#FFFFFF" size="12"></u-icon>
											</view>
										</u-number-box>
									</view>
								</view>
							</u-list-item>
						</u-list>
					</view>
				</view>
				<view v-else>
					<view>
						<u-button type="primary" text="添加新的用品申请" @click="appShow = true"></u-button>
					</view>
					<view>


						<view class="applicationBox" v-for="item in askRecords" :key="item._id">
							<view class="mainBox">
								<view class="recordTitle">{{ item.products[0].product }} × {{ item.products[0].number }}
								</view>
								<view class="note">
									申请类型：{{ claimTypeList.find(type => type.value === item.claimType)&&claimTypeList.find(type => type.value == item.claimType).name || '-' }}
								</view>
								<view class="note">申请理由：{{ item.notes }}</view>
								<view class="note">申请时间：{{ formatTime(item.createdAt ) }}</view>
								<u-steps :current="item.auditStatus+1" direction="column">
									<u-steps-item title="已申请" :desc="userInfo.name">
									</u-steps-item>
									<u-steps-item v-if="item.auditStatus===0" title="待审核" desc="管理员">
									</u-steps-item>
									<u-steps-item v-if="item.auditStatus===1" title="已审核" desc="管理员">
									</u-steps-item>
									<u-steps-item v-if="item.auditStatus===2" title="已拒绝" desc="管理员"
										:view="item.reason">
									</u-steps-item>
								</u-steps>
							</view>
						</view>

					</view>
				</view>
			</view>
			
			<!-- 记录 -->
			<view v-show="currentIndex === 2" class="tab-content">
				<view class="recordBox" v-for="item in receiveRecords" :key="item._id">
					<view class="recordTitle">{{ item.product }}等{{ item.number }}件</view>
					<view class="type online" v-if="item.recordSource === 1">线上申请</view>
					<view class="type under" v-else>计划领用</view>
					<view class="content">
						<view v-for="product in item.products" :key="product._id">{{ product.product }}
							({{ product.productSpec ? product.productSpec : '-' }}
							{{ product.modelNumber ? product.modelNumber : '' }}) × {{ product.number }}
						</view>
					</view>
					<view class="bottomBar">
						<view class="status" v-if="item.isRejected === false">
							<span class="circle bGreen"></span>
							<span>
								已签字
							</span>
						</view>
						<view class="status" v-else>
							<span class="circle bRed"></span>
							<span>
								已拒绝
							</span>
						</view>
						<view class="time">{{ item.receiveDate }}</view>
					</view>
				</view>
			</view>
			
			<!-- 防护用品清单 -->
			<view v-show="currentIndex === 3" class="tab-content">
				<view class="recordBox" v-for="item in protectList" :key="item._id">
					<view class="recordTitle">{{ item.product }}等{{ item.number }}件</view>
					<view class="type online" v-if="item.recordSource === 1">线上申请</view>
					<view class="type under" v-else>计划领用</view>
					<view class="content">
						<view v-for="product in item.products" :key="product._id">{{ product.product }}
							({{ product.productSpec ? product.productSpec : '-' }}
							{{ product.modelNumber ? product.modelNumber : '' }}) × {{ product.number }}
						</view>
					</view>
				</view>
			</view>
			
			<!-- 底部确认申请 -->
			<view v-if="appShow" class="bottom-confirm">
				<!-- <view class="bottom-text">当前已选{{ confirmNum }}件</view> -->
				<view class="bottom-text"></view>
				<view class="bottom-btn">
					<u-button type="primary" text="确认申请" @tap="dialogShow = true"></u-button>
				</view>
			</view>
			<!-- 领用计划选择申请类型 -->
			<view>
				<u-action-sheet :actions="menuList" :safeAreaInsetBottom="true" round="10" cancelText="取消"
					:title="menuTitle" :show="menuShow" @select="selectType" @close="closeSelectType"></u-action-sheet>
			</view>
			<view>
				<u-modal style="z-index: 10075 !important;" :show="dialogShow" :title="dialogTitle"
					@cancel="dialogCancel" @confirm="dialogConfirm" :showCancelButton="true" confirmText="确认申请">
					<view class="slot-content">
						<!-- wh分支使用表单 -->
						<template v-if="userInfo.branch === 'wh'">
							<u--form labelPosition="left" :model="formData" ref="uForm">
								<u-form-item label="申请类型" prop="claimType" borderBottom @click="showApplyType = true;">
									<u--input v-model="formData.claimTypeLabel" placeholder="请选择申请类型"
										border="none"></u--input>
									<u-icon slot="right" name="arrow-right"></u-icon>
								</u-form-item>
								<u-form-item label="申请理由" prop="reason" borderBottom>
									<u--input v-model="formData.reason" placeholder="请输入申请理由" border="none"></u--input>
								</u-form-item>
							</u--form>
							<u-action-sheet :show="showApplyType" :actions="claimTypeList" title="请选择申请类型"
								@close="showApplyType = false" @select="applyTypeSelect">
							</u-action-sheet>
							<u-toast ref="uToast"></u-toast>
						</template>
						<!-- 其他分支使用简单输入框 -->
						<template v-else>
							<u--input placeholder="请输入内容" border="bottom" v-model="reason" clearable></u--input>
						</template>
					</view>
				</u-modal>
			</view>
			<u-modal :show="confirmShow" :title="confirmTitle" :content='confirmContent' @confirm="signClick"
				showCancelButton @cancel="() => confirmShow = false"></u-modal>

			<u-popup :show="showSelectProduct" mode="bottom" height="600px">
				<view :style="{ height: scrollHeight }">
					<view class="selectProductTitle">请选择领取防护用品的具体规格型号</view>
					<view class="selectProductBody">

						<view v-for="(item,index) in productsList" :key="index">
							<!-- 产品型号信息部分 -->
							<view class="product-section">
								<view class="product-title">{{item.name}} × {{item.receiveNum}}</view>

								<u-radio-group v-model="item.selectData" shape="square" placement="column" @change="e => radioGroupChange(e, item)">
									<u-radio
										class="radioClass"
										style="margin: 10px 0"
										v-for="product in item.children" 
										:key="product._id" 
										:name="product._id"
										:label="(hasDisabled(product, item) ? '库存不足 | ' : '' ) + '规格：' + product.productSpec + ' | ' + (product.modelNumber ? '型号：' + product.modelNumber + ' | ' : '')"
										:disabled="hasDisabled(product, item)">
									</u-radio>
								</u-radio-group>
							</view>

							<!-- 生产日期选择部分 -->
							<view class="date-section" v-if="item.selectData && item.name === '头部防护/安全帽' && userInfo.branch === 'wh'">
								<view class="date-section-title-container">
									<view class="date-section-title">安全帽生产日期</view>
									<view class="help-icon" @click="showHelp = !showHelp">
										<u-icon name="question-circle" color="#2979ff" size="18"></u-icon>
									</view>
								</view>
								<view class="date-tip" v-if="showHelp">安全帽有效期为30天</view>
								<view class="date-selector" @click="showPicker = true">
									<text
										class="date-text">{{ formatProductionDate(item.productionDate) || '请选择生产日期' }}</text>
									<u-icon name="arrow-right" size="14" color="#666"></u-icon>
								</view>
							</view>
						</view>
					</view>
					<!-- 添加日期选择器组件 -->
					<u-datetime-picker :show="showPicker" v-model="productionDate" mode="date" @confirm="confirmDate"
						@cancel="showPicker = false"></u-datetime-picker>

					<view class="bottom-confirm">
						<u-button @click="closeSelectProduct">取消</u-button>
						<u-button type="primary" @click="confirmSelectProduct">确认</u-button>
					</view>
				</view>
			</u-popup>

		</view>
	</gracePage>
</template>
<script>
	import gracePage from "@/graceUI/components/gracePage.vue";
	import {
		mapGetters
	} from 'vuex'
	import config from "@/common.js";
	import employeeApi from '@/api/employee.js' //导入接口
	import moment from "moment";

	export default {
		data() {
			return {
				timeUnit: [{
						label: "天",
						value: "d"
					},
					{
						label: "周",
						value: "w"
					},
					{
						label: "月",
						value: "M"
					},
					{
						label: "季",
						value: "Q"
					},
					{
						label: "年",
						value: "y"
					},
				],
				tabList: [{
						name: '领用计划'
					},
					{
						name: '领用申请'
					},
					{
						name: '领用记录'
					},
					{
						name: '在用防护'
					}
				],
				currentIndex: 0, // 当前页面索引值
				typeList: [],
				navLeft: [], // 申请列表左侧
				appShow: false, // 申请界面
				type: "error",
				count: 0,
				confirmNum: 0,
				confirmIds: [], // 确认申请
				menuTitle: '请选择领用类型',
				menuList: [{
						name: '到期更换',
						value: 1
					},
					{
						name: '以旧换新',
						value: 2
					},
					{
						name: '按标准发放',
						value: 3
					}
				],
				// 新增仓库信息字段
				employeeWarehouse: null,
				claimTypeList: [{
						name: '到期更换',
						value: '1'
					},
					{
						name: '以旧换新',
						value: '2'
					},
					{
						name: '其他',
						value: '3'
					}
				],
				showApplyType: false,
				menuShow: false,
				dialogShow: false,
				dialogTitle: '申请',
				reason: '', // 申请理由
				receiveRecords: [], // 领用记录
				protectList: [], // 防护用品清单
				allProtectionPlan: [], // 发放计划
				askRecords: [], // 申请记录
				waitRecords: [], // 待领用
				waitApplyRecords: [], // 自己申请待领用
				info: false,
				logo: 'https://avatar.bbs.miui.com/images/noavatar_small.gif',
				confirmShow: false, // 确认框
				confirmTitle: '请签字确认',
				confirmContent: '',
				claimType: '',
				query: {},
				showSelectProduct: false,
				productsId: '',
				productsSelector: [{
						cateName: '1',
						id: 1
					},
					{
						cateName: '2',
						id: 2
					}
				],
				scrollHeight: '600px',
				productsList: [],
				formData: {
					claimType: '',
					reason: '',
					claimTypeLabel: ''
				},
				showPicker: false,
				productionDate: Number(new Date()),
				showHelp: false,
				warning: false,
				isTabSwitching: false, // 添加标签切换状态标志
			}
		},
		async created() {
			if (this.userInfo.companyStatus != 2) {
				uni.showToast({
					title: '企业还未成功绑定',
					icon: "none"
				});
				return;
			}
			this.getDefendproducts();
			this.getUserInfo(this.userInfo)
		},

		computed: {
			...mapGetters(['hasLogin', 'userInfo', 'path', 'aboutTransfer']),
		},

		onShow() {
			// 恢复原有功能
			console.log('userInfo', this.userInfo);
			console.log('hasLogin', this.hasLogin);
			console.log('path', this.path);
			console.log('aboutTransfer', this.aboutTransfer);
			uni.$on('refresh', data => {
				if (data.refresh) {
					this.refreshPage();
				}
			});
		},

		onReady() {
			// 页面渲染完成
		},

		onHide() {
			// 页面隐藏
		},

		onUnload() {
			// 页面卸载
		},

		onLoad() {
			// 页面加载
		},

		methods: {
			ableReceive(e) {
				if (moment().isAfter(moment(e))) {
					return true
				}
				return false
			},
			formatTime(e) {
				if (e) {
					return moment(e).format('YYYY-MM-DD')
				} else {
					return '-'
				}
			},
			formatTimeUnit(e) {
				const target = this.timeUnit.find(item => item.value === e)
				if (target) {
					return target.label
				}
				return ''

			},
			refreshPage() {
				console.log('B -> A');
				uni.redirectTo({
					url: './ppe' //这是你的当前页面地址
				});
			},

			// 优化的changeView方法
			changeView(e) {
				if (this.currentIndex === e.index) return;
				// 设置切换状态，激活遮罩层
				this.isTabSwitching = true;
				
				// 延迟更新视图状态，避免渲染竞争
				setTimeout(() => {
					// 更新索引和状态
					this.currentIndex = e.index;
					
					// 视图更新后处理
					this.$nextTick(() => {
						// 短暂延迟后移除遮罩层
						setTimeout(() => {
							this.isTabSwitching = false;
						}, 100);
					});
				}, 50);
			},

			// 选择类型（适配新的数据结构）
			selectType(e) {
				console.log(e);
				this.menuShow = false;
				this.query.claimType = e.value;
				if (this.query && this.query.data && this.query.data.recordSource == 1) {
					this.confirmShow = true;
					return
				}

				const products = this.query.data.products

				const productsList = []
				products.forEach(e => {
					// 适配新的数据结构
					let targetCategory = null;
					let targetSubCategory = [];

					if (e.categoryId) {
						// 新结构：使用categoryId查找分类
						targetCategory = this.navLeft.find(nL => nL._id === e.categoryId);
						if (targetCategory) {
							// 根据产品名称或分类名称匹配具体产品
							targetSubCategory = targetCategory.data.filter(product => {
								return product.product.includes(e.product) ||
									   product.product === e.product ||
									   e.categoryName === targetCategory.name;
							});
						}
					} else if (e.productType && Array.isArray(e.productType)) {
						// 旧结构：使用productType数组
						const category = e.productType[0]
						const subCategory = e.productType[1]
						targetCategory = this.navLeft.find(nL => nL.name === category)
						if (targetCategory) {
							targetSubCategory = targetCategory.data.filter(product => product.product === subCategory)
						}
					}

					if (targetCategory && targetSubCategory.length > 0) {
						productsList.push({
							name: e.categoryPath || (e.productType ? e.productType.join('/') : e.product),
							children: targetSubCategory,
							categoryId: targetCategory._id,
							selectData: '',
							selectProductSpec: '',
							productsOrder: e._id,
							receiveNum: e.number,
						})
					}
				})

				this.productsList = productsList
				console.log(9999, this.productsList)
				this.showSelectProduct = true
			},

			closeSelectType(e) {
				this.menuShow = false
			},

			closeSelectProduct() {
				this.showSelectProduct = false
			},
			confirmSelectProduct() {
				const that = this;
				console.log(this.productsList, 'this.productsList2222');
				if (!this.productsList.every(e => e.selectData)) {
					uni.showToast({
						title: '请先选择',
						duration: 2000,
						icon: 'error'
					});
					return
				}

				uni.showModal({
					title: '是否领取',
					content: '确认领取后，将前往签字确认',
					success: function(res) {

						const newParams = that.productsList.map(e => {
							// 适配新的数据结构
							const selectedProduct = e.children.find(child => child._id === e.selectData);

							return {
								// 优先使用新的productId字段
								productId: e.selectData,
								// 保留旧的productIds以兼容
								productIds: [e.categoryId, e.selectData],
								productsOrder: e.productsOrder,
								receiveNum: e.receiveNum,
								// 新增字段
								productSpec: selectedProduct ? selectedProduct.productSpec : '',
								modelNumber: selectedProduct ? selectedProduct.modelNumber : '',
								materialCode: selectedProduct ? selectedProduct.materialCode : ''
							}
						})

						const jsonStr = encodeURIComponent(JSON.stringify(newParams))

						if (res.confirm) {
							uni.navigateTo({
								url: `./ppeSign?_id=${that.query._id}&type=${that.query.type}&planId=${that.query.planId}&employee=${that.query.employee}&claimType=${that.query.claimType}&params=${jsonStr}&product=${that.query.product}&productionDate=${that.query.productionDate}`,
							})
						} else if (res.cancel) {

						}
					}
				});

			},

			radioGroupChange(e, item) {
				let target = null
				this.navLeft.forEach(nL => {
					nL.data.forEach(item => {
						if (item._id === e) {
							target = item
						}
					})
				})

				if (target) {
					item.selectProductSpec = target.productSpec
					// item.modelNumber = target.modelNumber
					if (parseInt(item.receiveNum) > parseInt(target.surplus)) {
						uni.showToast({
							title: '库存不足',
							duration: 2000,
							icon: 'error'
						});

						item.selectData = ''
					}
				}
			},

			hasDisabled(product, item) {
				if (!product.surplus) {
					return true
				}
				if (parseInt(item.receiveNum) > parseInt(product.surplus)) {
					return true
				}
				return false
			},

			// 对话框取消
			dialogCancel() {
				this.dialogShow = false;
				uni.showToast({
					title: "取消申请",
					icon: "none"
				});
			},

			// 确认签字
			async dialogConfirm() {
				let claimType = ''
				if (this.userInfo.branch === 'wh') {
					this.reason = this.formData.reason
					claimType = this.formData.claimType
				}
				if (this.userInfo.branch === 'wh' && !claimType) {
					this.$refs.uToast.show({
						type: "error",
						icon: false,
						message: "申请类型不能为空！",
					})
					return;
				}
				if (this.reason === '') {
					this.$refs.uToast.show({
						type: "error",
						icon: false,
						message: "申请理由不能为空！",
					})
					return;
				}
				this.dialogShow = false;
				this.appShow = false;
				this.confirmIds.forEach(item => {
					item.notes = this.reason;
					item.claimType = claimType;
					delete item.data;
				})
				const params = {
					EnterpriseID: this.userInfo.companyId.length ? this.userInfo.companyId[0] : '',
					type: 'application',
					arr: this.confirmIds
				}
				console.log(params, 'params');
				await employeeApi.receiveProducts(params)
				uni.redirectTo({
					url: './ppe' //这是你的当前页面地址
				});
			},

			// 添加用品
			async valChange(val,item) {
				console.log(val,item, 'val,item');
				setTimeout(() => {
					let temp = false; // 判断是否没有这个数据
					this.confirmIds.forEach(item => {
						if (item._id === val._id) {
							item.num = val.selectedNum;
							temp = true;
							return;
						}
					})
					if (temp === false) {
						const obj = {
							num: val.selectedNum,
							employee: this.userInfo.employeeId,
							employeeName: this.userInfo.name,
							...val,
						}
						if (this.userInfo.branch === 'wh') {
							obj.productSpecId = val._id;
							obj._id = item._id;
							obj.name = val.product;
						}
						this.confirmIds.push(obj)
					}
				}, 50)
				this.confirmIds = this.confirmIds.filter(obj => obj.num !== 0);
				this.confirmNum = this.confirmIds.length;
			},

			getUserInfo(val) {
				const loginType = uni.getStorageSync('loginType')
				if (!this.hasLogin) return
				if (loginType && val[loginType]) {
					debugger
					this.logo = val[loginType].logo || 'https://avatar.bbs.miui.com/images/noavatar_small.gif'
					this.userName = val[loginType].nickName
				} else {
					this.logo = val.logo || 'https://avatar.bbs.miui.com/images/noavatar_small.gif'
					this.userName = val.name || val.userName
				}
			},

			async getDefendproducts() {
				// 获取防护用品领用记录（适配新的API响应结构）
				const userInfo = this.userInfo
				const res = await employeeApi.getDefendproducts(userInfo);
				if (res.status === 200) {
					// 配发标准
					this.allProtectionPlan = res.data.allProtectionPlan || []

					// 仓库信息（新增）
					if (res.data.employeeWarehouse) {
						this.employeeWarehouse = res.data.employeeWarehouse
						console.log('员工仓库信息:', this.employeeWarehouse)
					}

					// 待领用列表
					const records = res.data.records || []
					records.forEach(record => {
						if (record.products) {
							for (let i = 0; i < record.products.length; i++) {
								const item = record.products[i];
								record.product = record.products[0].product
								record.number = record.number ? record.number + item.number : item.number;
							}
						}
						if (!record.receiveDate) { // 没有被领取
							if (record.receiveStartDate) { // 有领取开始时间
								if (record.recordSource === 1) {
									this.waitApplyRecords.push(record)
								}
								const now = new Date();
								record.receiveStartDate > now.toISOString() ? '' : this.waitRecords.push(
									record);
							} else { // 没有领取开始时间
								this.waitRecords.push(record);
							}
						} else { // 已经被领取
							this.receiveRecords.push(record)
						}
					})

					// 防护用品清单
					this.protectList = this.receiveRecords.filter(item => !item.scrap && item.sign && !item
						.isRejected);

					// 用品类型（适配新的数据结构）
					if (res.data.typeList && res.data.typeList.list) {
						res.data.typeList.list.forEach(item => {
							let products = '';
							if (item.data && Array.isArray(item.data)) {
								item.data.forEach(item1 => {
									if (products === '') {
										products = item1.modelNumber || item1.productSpec || '';
									} else {
										products = products + ',' + (item1.modelNumber || item1.productSpec || '');
									}
									item1.selectedNum = 0
								});
							}

							// 处理图标路径
							if (item.tableHeader) {
								if (item.tableHeader.startsWith('http')) {
									// 已经是完整URL
								} else if (item.tableHeader.startsWith('static/')) {
									item.tableHeader = config.apiServer + item.tableHeader;
								}
							}

							item.selectedNum = 0;
							item.products = products;
							this.navLeft.push(item);
						})
					}

					// 申请列表
					const applications = res.data.applications || []
					applications
						.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt)) // 按创建时间逆序排序
						.forEach(application => {
							this.askRecords.push(application);
						});
				}
			},
			handleReceive(productItem, item, type) {
				if (productItem.todo) {
					const data = productItem.todo
					data.product = productItem.todo.products[0].product
					this.receiveBtn(productItem.todo, type)
				}
			},
			// 确认领用
			receiveBtn(item, type) {
				this.query = {
					_id: item._id,
					planId: item.planId,
					employee: item.employee,
					type,
					data: item,
					product: item.product,
					productionDate: '',
				}
				console.log(item, 'item')
				if (type === 'receive') {
					this.confirmContent = '是否领取'
					if (this.userInfo.branch === 'wh') { // 万华分支直接选择物品，不需要选择类别
						this.selectType({
							value: 1
						})
						return
					}
					this.menuShow = true;
				} else if (type === 'reject') {
					this.confirmContent = '是否要拒绝领取'
					this.confirmShow = true;
				}
			},

			// 签字
			signClick() {
				console.log(this.query, 'this.query');
				const that = this
				this.confirmShow = false;
				uni.navigateTo({
					url: `./ppeSign?_id=${that.query._id}&type=${that.query.type}&planId=${that.query.planId}&employee=${that.query.employee}&claimType=${that.query.claimType}&product=${that.query.product}&productionDate=${that.query.productionDate}`,
				})
			},
			toggleExpand(item) {
				// 使用 Vue.set 确保响应性
				this.$set(item, 'isExpanded', !item.isExpanded);
				console.log(item, 'item')
			},
			applyTypeSelect(e) {
				this.formData.claimType = e.value
				this.formData.claimTypeLabel = e.name
			},

			confirmDate(e) {
				if (!e.value) {
					this.$refs.uToast.show({
						type: "error",
						icon: false,
						message: "请选择生产日期",
					})
					return
				}

				const date = new Date(e.value);
				this.productsList.forEach(item => {
					if (item.name === '头部防护/安全帽') {
						// 直接存储格式化后的日期字符串，而不是Date对象
						item.productionDate = moment(date).format('YYYY-MM-DD');
					}
				})
				this.query.productionDate = moment(date).format('YYYY-MM-DD');
				this.showPicker = false;
			},

			formatProductionDate(date) {
				if (!date) return '';
				// 如果已经是字符串格式，直接返回
				if (typeof date === 'string') return date;
				// 如果是Date对象，格式化为YYYY-MM-DD
				return moment(date).format('YYYY-MM-DD');
			},
		},

		components: {
			gracePage,
		}
	}
</script>

<style>
	.grace-page-body {
		height: 100%;
		background-color: #F6F6F6;
		position: relative;
	}

	.u-tabs {
		margin-bottom: .6rem;
	}

	.u-button {
		margin-right: 1rem;
	}
</style>
<style scoped lang="scss">
	.imgBox {
		text-align: center;
		padding: 2rem;

		img {
			width: 15rem;
		}
	}

	// 领用
	.receiveBox {
		background-color: #ffffff;
		padding: 1rem 1rem;
		border-radius: .5rem;
		display: flex;
		justify-content: flex-start;
		flex-direction: column;

		.contain {
			display: flex;
			justify-content: flex-start;
			gap: 1rem;

			.mainBox {
				width: 50%;

				.recordTitle {
					font-weight: 600;
					color: #555555;
					margin-bottom: .4rem;
				}

				.type {
					font-size: 12px;
					height: 1.6rem;
					line-height: 1.6rem;
					font-weight: 600;
					text-align: center;
					margin-bottom: .5rem;
				}

				.under {
					background: #e1f3d8;
					color: #67c23a;
				}

				.online {
					color: #1678ff;
					background: #daecff;
				}

				.no {
					background: rgba(0, 0, 0, 0.10);
					color: #aaaaaa;
				}

				.pass {
					background: #ffe4cb;
					color: #f97d0b;
				}

				.content {
					color: #bcbcbc;
				}
			}

			.btnBar {
				// width: 50%;
				display: flex;
				align-items: center;
				flex-wrap: nowrap; // 默认不换行
				justify-content: flex-start;

				:deep(.u-button) {
					min-width: 60px; // 按钮最小宽度
				}
			}
		}

		.timeBox {
			color: #F46C6C;
		}
	}

	// 申请的样式
	.center {
		width: 100%;

		.list {
			display: flex;

			.center-body {
				height: 84vh;
				background-color: #ffffff;
				overflow: auto;
				flex: 1;

				.list-box {
					display: flex;
					// justify-content: space-between;
					justify-content: flex-start;
					margin: .5rem .8rem .5rem;
					padding-right: .8rem;
					box-shadow: 0px 10px 20px 0px rgba(0, 0, 0, 0.04);
				}

				.main-box {
					display: flex;
					height: 5rem;
					width: 70%;

					.img-box {
						width: 5rem;
						height: 5rem;
						display: flex;
						align-items: center;
						justify-content: center;
						margin-right: 1.5rem;
						overflow: hidden;

						img {
							width: 100%;
							height: 100%;
							object-fit: contain;
						}
					}

					.text-box {
						width: 40%;
						padding-left: 1rem;
						display: flex;
						flex-direction: column;
						justify-content: space-between;
						padding: .4rem;
						width: 10rem;
						line-height: 4rem;

						.title {
							color: #303133;
							font-weight: 600;
						}
					}

					.arrow-icon {
						position: absolute;
						right: 10px;
						top: 50%;
						transform: translateY(-50%);
					}
				}

				.select-box {
					width: 30%;
					height: 5rem;
					line-height: 5rem;

					.minus {
						width: 1.2rem;
						height: 1.2rem;
						background-color: #3E73FE;
						border-radius: 50%;
						display: flex;
						justify-content: center;
						align-items: center;
					}

					.input {
						padding: 0 10px;
					}

					.plus {
						width: 1.2rem;
						height: 1.2rem;
						background-color: #3E73FE;
						border-radius: 50%;
						display: flex;
						justify-content: center;
						align-items: center;
					}
				}
			}
		}

		.applicationBox {
			background-color: #ffffff;
			padding: 1rem 1.5rem;
			margin: 1rem;
			border-radius: .5rem;
			display: flex;
			justify-content: space-between;

			.mainBox {
				flex: 1;

				.recordTitle {
					font-weight: 600;
					color: #555555;
					margin-bottom: .5rem;
				}

				.note {
					line-height: 1.5rem;
				}

				.type {
					width: 4rem;
					height: 1.6rem;
					line-height: 1.6rem;
					font-weight: 600;
					text-align: center;
					margin: .3rem 0;
				}

				.under {
					background: #e1f3d8;
					color: #67c23a;
				}

				.no {
					background: rgba(0, 0, 0, 0.10);
					color: #aaaaaa;
				}

				.pass {
					background: #ffe4cb;
					color: #f97d0b;
				}

				.content {
					line-height: 1.5rem;
					font-size: 1rem;
					font-weight: 400;
					color: #bcbcbc;
					margin-bottom: .7rem;
				}
			}

		}
	}

	// 底部确认申请的样式
	.bottom-confirm {
		width: 100%;
		height: 5rem;
		box-shadow: 0px 1px 7px 1px rgba(0, 0, 0, 0.20);
		background-color: #ffffff;
		position: fixed;
		bottom: 0;
		left: 0;
		z-index: 999;
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding-bottom: 1rem;

		.bottom-text {
			font-size: .9rem;
			font-weight: 500;
			// color: #606266;
			line-height: .9rem;
			margin-left: 1rem;
		}

		.bottom-btn {
			width: 5rem;
			margin-right: 1rem;
		}
	}

	// 记录的样式
	.recordBox {
		background-color: #ffffff;
		padding: 1rem 1.5rem;
		border-radius: .5rem;

		.recordTitle {
			font-weight: 600;
			color: #555555;
			margin-bottom: .4rem;
		}

		.type {
			width: 4.2rem;
			height: 1.6rem;
			line-height: 1.6rem;
			font-weight: 600;
			text-align: center;
			margin-bottom: .3rem;
		}

		.online {
			color: #1678ff;
			background: #daecff;
		}

		.under {
			background: #e1f3d8;
			color: #67c23a;
		}

		.content {
			line-height: 1.4rem;
			font-size: 1rem;
			font-weight: 400;
			color: #bcbcbc;
			margin-bottom: .3rem;
		}

		.bottomBar {
			display: flex;
			justify-content: space-between;

			.status {
				font-size: .8rem;
				font-weight: 400;
				color: #555555;
				line-height: .8rem;

				.circle {
					width: .4rem;
					height: .4rem;
					border-radius: 50%;
					display: inline-block;
					margin-right: .2rem;
					/* 调整小圆点与文本之间的间距 */
					margin-bottom: .1rem;
				}

				.bGreen {
					background-color: #67C23A;
				}

				.bGray {
					background-color: #BCBCBC;
				}

				.bRed {
					background-color: #E02020;
				}
			}

			.time {
				font-size: .8rem;
				font-weight: 400;
				line-height: .8rem;
				color: #bcbcbc;
			}
		}
	}

	.selectProductTitle {
		text-align: center;
		padding: 10px;
		font-weight: bold;
	}

	.selectProductBody {
		height: 450px;
		overflow: scroll;
		// border: 1px solid red;
		padding: 10px;
	}

	::v-deep .u-radio {
		margin: 10px 0 !important;
	}

	.plan {
		padding: 10px;
		border: 1px solid #c8c9cc;

		.planItem {
			padding: 10px;
			display: flex;
			justify-content: space-between;
		}

		.planButton {
			display: inline-block;
			margin-right: 10px;
		}
	}

	.receiveBoxTitle {
		margin-bottom: 20px;
		color: #2b85e4;
	}

	// 添加媒体查询，处理超小屏幕
	@media screen and (max-width: 375px) {
		.receiveBox {
			.contain {
				flex-direction: column; // 在超小屏幕上改为垂直布局

				.btnBar {
					justify-content: flex-start;
					margin-top: 0.5rem;
				}
			}
		}
	}

	.list-box {
		position: relative;

		.expand-icon {
			position: absolute;
			right: 10px;
			top: 50%;
			transform: translateY(-50%);
		}
	}

	.sub-list {
		background: #f8f8f8;
		margin: 0 0.8rem;
		border-radius: 4px;

		.sub-item {
			display: flex;
			justify-content: space-between;
			align-items: center;
			border-bottom: 1px solid #eee;

			&:last-child {
				border-bottom: none;
			}

			.text-box {
				width: 50%;
				text-align: center;
			}

			.select-box {
				height: 5rem;
				line-height: 5rem;

				.minus {
					width: 1.2rem;
					height: 1.2rem;
					background-color: #3E73FE;
					border-radius: 50%;
					display: flex;
					justify-content: center;
					align-items: center;
				}

				.input {
					padding: 0 10px;
				}

				.plus {
					width: 1.2rem;
					height: 1.2rem;
					background-color: #3E73FE;
					border-radius: 50%;
					display: flex;
					justify-content: center;
					align-items: center;
				}
			}
		}
	}

	.date-picker-box {
		margin: 10px 0;
		padding: 10px;
		background: #fff;
		border-radius: 4px;

		.date-picker-title {
			font-size: 14px;
			color: #666;
			margin-bottom: 8px;
		}

		.date-picker-content {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 8px;
			background: #f8f8f8;
			border-radius: 4px;

			text {
				color: #333;
			}
		}
	}

	.product-section {
		background-color: #ffffff;
		border-radius: 8px;
		padding: 12px;
		margin-bottom: 15px;
		box-shadow: 0 1px 5px rgba(0, 0, 0, 0.05);

		.product-title {
			font-size: 16px;
			font-weight: 500;
			color: #333;
			margin-bottom: 12px;
			padding-bottom: 8px;
			border-bottom: 1px solid #eee;
		}
	}

	.date-section {
		background-color: #f8f8ff; // 使用略微不同的背景色区分
		border-radius: 8px;
		padding: 12px;
		margin-bottom: 20px;
		margin-top: 5px;
		border: 1px solid #e2e6ff;

		.date-section-title-container {
			display: flex;
			align-items: center;
			margin-bottom: 10px;
		}

		.date-section-title {
			font-size: 15px;
			font-weight: 500;
			color: #2979ff;
			margin-bottom: 0;
		}

		.help-icon {
			margin-left: 5px;
			display: flex;
			align-items: center;
		}

		.date-tip {
			font-size: 12px;
			color: #ff9800;
			margin-bottom: 10px;
			padding-left: 2px;
		}

		.date-selector {
			display: flex;
			justify-content: space-between;
			align-items: center;
			background-color: #ffffff;
			padding: 10px 15px;
			border-radius: 6px;
			border: 1px solid #e1e4f3;

			.date-text {
				color: #333;
				font-size: 14px;
			}
		}
	}

	// 为selectProductBody添加一些改进
	.selectProductBody {
		height: 450px;
		overflow-y: auto;
		padding: 15px;

		// 添加底部空间以确保在滚动时有足够空间查看所有内容
		padding-bottom: 30px;
	}

	.tabs-container {
		position: relative;
		width: 100% !important;
		height: 100%;
		overflow: hidden;
	}
	
	.tab-content {
		width: 100%;
		height: calc(100% - 2.6rem); /* 减去tabs的高度 */
		overflow-y: auto;
		background-color: #F6F6F6;
		position: relative; /* 确保定位上下文 */
		z-index: 1; /* 确保正常z-index堆叠 */
		padding-bottom: 6rem; /* 添加底部内边距，为固定的bottom-confirm腾出空间 */
	}
	
	.tab-mask {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: #F6F6F6;
		z-index: 9999; /* 确保在最上层 */
		opacity: 1; /* 完全不透明 */
	}
</style>